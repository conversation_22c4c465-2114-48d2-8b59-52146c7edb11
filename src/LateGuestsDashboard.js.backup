import React, { useState, useEffect, useCallback } from 'react';
import { db } from "./firebase";
import { doc, getDoc, setDoc } from "firebase/firestore";
import { Pie } from "react-chartjs-2";
import "chart.js/auto";
import emailjs from '@emailjs/browser';  // Added import for emailjs

const LateGuestsDashboard = () => {
  const [lateGuests, setLateGuests] = useState([]);
  const [filteredGuests, setFilteredGuests] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterBy, setFilterBy] = useState("all");
  const [approvalFilter, setApprovalFilter] = useState("all");
  const [statView, setStatView] = useState("behalfOf"); // NEW: chart filter

  // Added missing state variables for password modal and email sending status
  const [currentPassword, setCurrentPassword] = useState("");
  const [currentGuestIndex, setCurrentGuestIndex] = useState(null);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [emailSendingStatus, setEmailSendingStatus] = useState({});

  const [stats, setStats] = useState({
    total: 0,
    onesmus: 0,
    colleta: 0,
    approved: 0,
    pending: 0,
    declined: 0,
    titles: {}
  });

  // Edit states for Late Guests
  const [editMode, setEditMode] = useState(false);
  const [editIndex, setEditIndex] = useState(null);
  const [editedGuest, setEditedGuest] = useState({
    title: "",
    name: "",
    surname: "",
    email: "",
    phone: "",
    behalfOf: "",
    approvalStatus: "pending",
    password: ""
  });

  // Update stats
  const updateStats = useCallback((data) => {
    const titleCounts = {};
    data.forEach(guest => {
      const title = guest.title || "Unspecified";
      titleCounts[title] = (titleCounts[title] || 0) + 1;
    });

    setStats({
      total: data.length,
      onesmus: data.filter((g) => g.behalfOf === "Onesmus").length,
      colleta: data.filter((g) => g.behalfOf === "Colleta").length,
      approved: data.filter((g) => g.approvalStatus === "approved").length,
      pending: data.filter((g) => g.approvalStatus === "pending").length,
      declined: data.filter((g) => g.approvalStatus === "declined").length,
      titles: titleCounts
    });
  }, []);

  // Added generatePassword function
  const generatePassword = (length = 8) => {
    const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
    let password = "";
    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * charset.length);
      password += charset[randomIndex];
    }
    return password;
  };

  // Fetch late guests
  const fetchLateGuests = useCallback(async () => {
    try {
      const lateListRef = doc(db, "guests", "late_list");
      const lateListDoc = await getDoc(lateListRef);

      if (lateListDoc.exists()) {
        const data = lateListDoc.data();
        let guestArray = data.guests || [];
        
        guestArray = guestArray.map(guest => ({
          ...guest,
          approvalStatus: guest.approvalStatus || "pending",
          password: guest.password || ""
        }));

        setLateGuests(guestArray);
        setFilteredGuests(guestArray);
        updateStats(guestArray);
      }
    } catch (error) {
      console.error("Error fetching late guests:", error);
    }
  }, [updateStats]);

  // Handle search
  const handleSearch = (event) => {
    const term = event.target.value.toLowerCase();
    setSearchTerm(term);
    applyFilters(term, filterBy, approvalFilter);
  };

  // Apply filters
  const applyFilters = useCallback((term, filter, approvalFilter) => {
    let filtered = lateGuests.filter((guest) => {
      const matchesSearch =
        guest.name.toLowerCase().includes(term) ||
        guest.surname.toLowerCase().includes(term) ||
        guest.email.toLowerCase().includes(term) ||
        guest.phone.includes(term);

      const matchesBehalfOf = filter === "all" || guest.behalfOf === filter;
      const matchesApproval = approvalFilter === "all" || guest.approvalStatus === approvalFilter;

      return matchesSearch && matchesBehalfOf && matchesApproval;
    });

    setFilteredGuests(filtered);
  }, [lateGuests]);

  const handleFilterChange = (event) => {
    const value = event.target.value;
    setFilterBy(value);
    applyFilters(searchTerm, value, approvalFilter);
  };

  const handleApprovalFilterChange = (event) => {
    const value = event.target.value;
    setApprovalFilter(value);
    applyFilters(searchTerm, filterBy, value);
  };

  const handleApprovalChange = async (index, status) => {
    try {
      // Find the actual guest in the lateGuests array by index
      const updatedGuests = [...lateGuests];

      // Generate password only if changing to "approved" and no password exists
      let password = "";
      if (status === "approved" && !updatedGuests[index].password) {
        password = generatePassword();
        setCurrentPassword(password);
        setCurrentGuestIndex(index);
        setShowPasswordModal(true);
      }

      // Update the guest approval status and password locally
      updatedGuests[index] = {
        ...updatedGuests[index],
        approvalStatus: status,
        password: status === "approved" ? (password || updatedGuests[index].password || generatePassword()) : "",
      };

      // Update Firestore first to ensure database consistency
      const lateListRef = doc(db, "guests", "late_list");
      await setDoc(lateListRef, { guests: updatedGuests }, { merge: true });
      // console.log(`Late guest approval status updated to ${status} in Firestore`);

      // Update local state
      setLateGuests(updatedGuests);
      setFilteredGuests(updatedGuests);
      updateStats(updatedGuests);

      // If approved, send email using EmailJS
      if (status === "approved") {
        setEmailSendingStatus(prev => ({
          ...prev,
          [updatedGuests[index].email]: "sending"
        }));

        const guest = updatedGuests[index];

        try {
          const response = await emailjs.send(
            process.env.REACT_APP_EMAILJS_SERVICE_ID,
            process.env.REACT_APP_EMAILJS_TEMPLATE_ID,
            {
              to_name: `${guest.title || ""} ${guest.name} ${guest.surname}`,
              guest_email: guest.email,
              guest_password: guest.password,
              reply_to: "<EMAIL>"
            }
          );

          if (response.status === 200) {
            // Update email status to sent
            setEmailSendingStatus(prev => ({
              ...prev,
              [guest.email]: "sent"
            }));

            // Update guest emailSent status locally and in Firestore
            const emailSentGuests = [...updatedGuests];
            emailSentGuests[index] = {
              ...emailSentGuests[index],
              emailSent: true
            };

            await setDoc(lateListRef, { guests: emailSentGuests }, { merge: true });

            setLateGuests(emailSentGuests);
            setFilteredGuests(emailSentGuests);
          }
        } catch (error) {
          console.error("Error sending email:", error);
          setEmailSendingStatus(prev => ({
            ...prev,
            [guest.email]: "error"
          }));
        }
      }

    } catch (error) {
      console.error("Error updating late guest:", error);
      alert(`Failed to update guest status to ${status}. Please try again.`);
    }
  };


  const exportToCSV = () => {
    const headers = ["Title", "Name", "Surname", "Email", "Phone", "Behalf Of", "Approval Status"];
    const csvData = filteredGuests.map((guest) =>
      [guest.title || "", guest.name, guest.surname, guest.email, guest.phone, guest.behalfOf, guest.approvalStatus].join(",")
    );

    const csv = [headers.join(","), ...csvData].join("\n");
    const blob = new Blob([csv], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "late_wedding_guests.csv";
    a.click();
    window.URL.revokeObjectURL(url);
  };

  // Handle edit initiation
  const handleEditClick = (index) => {
    setEditMode(true);
    setEditIndex(index);
    setEditedGuest({...filteredGuests[index]});
  };

  // Save edited guest
  const saveEditedGuest = async () => {
    try {
      const updatedGuests = [...lateGuests];
      updatedGuests[editIndex] = editedGuest;

      setLateGuests(updatedGuests);
      setFilteredGuests(updatedGuests);
      updateStats(updatedGuests);

      const lateListRef = doc(db, "guests", "late_list");
      await setDoc(lateListRef, { guests: updatedGuests }, { merge: true });

      setEditMode(false);
      setEditIndex(null);
      setEditedGuest({
        title: "",
        name: "",
        surname: "",
        email: "",
        phone: "",
        behalfOf: "",
        approvalStatus: "pending",
        password: ""
      });
    } catch (error) {
      console.error("Error updating late guest:", error);
    }
  };

  // Cancel editing
  const cancelEdit = () => {
    setEditMode(false);
    setEditIndex(null);
    setEditedGuest({
      title: "",
      name: "",
      surname: "",
      email: "",
      phone: "",
      behalfOf: "",
      approvalStatus: "pending",
      password: ""
    });
  };

  // Handle input changes in edit form
  const handleEditInputChange = (e) => {
    const { name, value } = e.target;
    setEditedGuest(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Delete guest
  const deleteGuest = async (index) => {
    if (!window.confirm("Are you sure you want to delete this guest?")) return;

    try {
      const updatedGuests = [...lateGuests];
      updatedGuests.splice(index, 1);
      setLateGuests(updatedGuests);
      setFilteredGuests(updatedGuests);
      updateStats(updatedGuests);

      const lateListRef = doc(db, "guests", "late_list");
      await setDoc(lateListRef, { guests: updatedGuests }, { merge: true });
    } catch (error) {
      console.error("Error deleting late guest:", error);
    }
  };

  useEffect(() => {
    fetchLateGuests();
  }, [fetchLateGuests]);

  const getChartColors = (type) => {
    if (type === 'behalfOf') {
      return {
        backgroundColors: ["#6D8B74", "#D9CAB3"],
        hoverColors: ["#5A7561", "#B8A992"]
      };
    } else if (type === 'approval') {
      return {
        backgroundColors: ["#4CAF50", "#FFC107", "#F44336"],
        hoverColors: ["#43A047", "#FFB300", "#E53935"]
      };
    } else {
      const baseColors = [
        '#6D8B74', '#D9CAB3', '#5F7161', '#EFEAD8', 
        '#4B644A', '#C2B092', '#3C4F3A', '#A29171'
      ];
      const backgroundColors = [];
      const hoverColors = [];
      const titles = Object.keys(stats.titles);
      titles.forEach((_, index) => {
        const colorIndex = index % baseColors.length;
        backgroundColors.push(baseColors[colorIndex]);
        hoverColors.push(baseColors[colorIndex] + '90');
      });
      return { backgroundColors, hoverColors };
    }
  };

  const getChartData = () => {
    if (statView === "behalfOf") {
      return {
        labels: ["Onesmus' Guests", "Colleta's Guests"],
        datasets: [{
          data: [stats.onesmus, stats.colleta],
          backgroundColor: getChartColors('behalfOf').backgroundColors,
          hoverBackgroundColor: getChartColors('behalfOf').hoverColors,
        }]
      };
    }
    if (statView === "approval") {
      return {
        labels: ["Approved", "Pending", "Declined"],
        datasets: [{
          data: [stats.approved, stats.pending, stats.declined],
          backgroundColor: getChartColors('approval').backgroundColors,
          hoverBackgroundColor: getChartColors('approval').hoverColors,
        }]
      };
    }
    if (statView === "titles") {
      return {
        labels: Object.keys(stats.titles),
        datasets: [{
          data: Object.values(stats.titles),
          backgroundColor: getChartColors('titles').backgroundColors,
          hoverBackgroundColor: getChartColors('titles').hoverColors,
        }]
      };
    }
  };

  return (
    <div className="container">
      <h1>Late Guest Dashboard</h1>
      <p className="subtitle">Managing extra guests from the late_list collection</p>

      {/* Stats */}
      <div className="stats-container">
        <div className="stats-card"><p>Total Late Guests</p><span>{stats.total}</span></div>
        <div className="stats-card"><p>Onesmus's Late Guests</p><span>{stats.onesmus}</span></div>
        <div className="stats-card"><p>Colleta's Late Guests</p><span>{stats.colleta}</span></div>
        <div className="stats-card"><p>Approved</p><span>{stats.approved}</span></div>
        <div className="stats-card"><p>Pending</p><span>{stats.pending}</span></div>
        <div className="stats-card"><p>Declined</p><span>{stats.declined}</span></div>
      </div>

      {/* Chart View Toggle */}
      <div className="chart-toggle">
        <button onClick={() => setStatView("behalfOf")} className={statView === "behalfOf" ? "active" : ""}>View by Behalf Of</button>
        <button onClick={() => setStatView("titles")} className={statView === "titles" ? "active" : ""}>View by Titles</button>
        <button onClick={() => setStatView("approval")} className={statView === "approval" ? "active" : ""}>View by Approval Status</button>
      </div>

      {/* Single Chart Area */}
      <div className="chart-container">
        <Pie data={getChartData()} />
      </div>

      {/* Filters */}
      <div className="filters">
        <input type="text" placeholder="Search late guests..." value={searchTerm} onChange={handleSearch} />
        <select value={filterBy} onChange={handleFilterChange}>
          <option value="all">All Guests</option>
          <option value="Onesmus">Onesmus's Guests</option>
          <option value="Colleta">Colleta's Guests</option>
        </select>
        <select value={approvalFilter} onChange={handleApprovalFilterChange}>
          <option value="all">All Statuses</option>
          <option value="pending">Pending</option>
          <option value="approved">Approved</option>
          <option value="declined">Declined</option>
        </select>
        <button onClick={exportToCSV}>Export CSV</button>
      </div>

      {/* Table */}
      <div className="table-container">
        <table>
          <thead>
            <tr>
              <th>Title</th>
              <th>Name</th>
              <th>Surname</th>
              <th>Email</th>
              <th>Phone</th>
              <th>Behalf Of</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredGuests.map((guest, index) => (
              <tr key={index} className={`status-${guest.approvalStatus}`}>
                <td>{guest.title || "—"}</td>
                <td>{guest.name}</td>
                <td>{guest.surname}</td>
                <td>{guest.email}</td>
                <td>{guest.phone}</td>
                <td>{guest.behalfOf}</td>
                <td className={`status-cell ${guest.approvalStatus}`}>
                  {guest.approvalStatus.charAt(0).toUpperCase() + guest.approvalStatus.slice(1)}
                </td>
                <td className="action-buttons">
                  {guest.approvalStatus !== "approved" && (
                    <button onClick={() => handleApprovalChange(index, "approved")} className="approve-button" title="Approve">✓</button>
                  )}
                  {guest.approvalStatus !== "declined" && (
                    <button onClick={() => handleApprovalChange(index, "declined")} className="decline-button" title="Decline">✗</button>
                  )}
                  <button onClick={() => handleEditClick(index)} className="edit-button" title="Edit">✎</button>
                  <button onClick={() => deleteGuest(index)} className="delete-button" title="Delete">🗑️</button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Edit Form Modal */}
      {editMode && (
        <div className="edit-form-overlay">
          <div className="edit-form">
            <h2>Edit Guest</h2>
            <div className="form-group">
              <label>Title:</label>
              <input
                type="text"
                name="title"
                value={editedGuest.title}
                onChange={handleEditInputChange}
              />
            </div>
            <div className="form-group">
              <label>Name:</label>
              <input
                type="text"
                name="name"
                value={editedGuest.name}
                onChange={handleEditInputChange}
              />
            </div>
            <div className="form-group">
              <label>Surname:</label>
              <input
                type="text"
                name="surname"
                value={editedGuest.surname}
                onChange={handleEditInputChange}
              />
            </div>
            <div className="form-group">
              <label>Email:</label>
              <input
                type="email"
                name="email"
                value={editedGuest.email}
                onChange={handleEditInputChange}
              />
            </div>
            <div className="form-group">
              <label>Phone:</label>
              <input
                type="text"
                name="phone"
                value={editedGuest.phone}
                onChange={handleEditInputChange}
              />
            </div>
            <div className="form-group">
              <label>Behalf Of:</label>
              <select
                name="behalfOf"
                value={editedGuest.behalfOf}
                onChange={handleEditInputChange}
              >
                <option value="Onesmus">Onesmus</option>
                <option value="Colleta">Colleta</option>
              </select>
            </div>
            <div className="form-group">
              <label>Approval Status:</label>
              <select
                name="approvalStatus"
                value={editedGuest.approvalStatus}
                onChange={handleEditInputChange}
              >
                <option value="pending">Pending</option>
                <option value="approved">Approved</option>
                <option value="declined">Declined</option>
              </select>
            </div>
            <div className="form-buttons">
              <button onClick={saveEditedGuest}>Save</button>
              <button onClick={cancelEdit} className="cancel-button">Cancel</button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LateGuestsDashboard;
