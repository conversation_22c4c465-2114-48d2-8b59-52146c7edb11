import React, { useState, useEffect, useCallback } from 'react';
import { db } from "./firebase";
import { doc, getDoc, setDoc } from "firebase/firestore";
import { Pie } from "react-chartjs-2";
import "chart.js/auto";
import emailjs from '@emailjs/browser';

const LateGuestsDashboard = () => {
  const [lateGuests, setLateGuests] = useState([]);
  const [filteredGuests, setFilteredGuests] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterBy, setFilterBy] = useState("all");
  const [approvalFilter, setApprovalFilter] = useState("all");
  const [statView, setStatView] = useState("behalfOf");

  // Password modal and email sending status
  const [currentPassword, setCurrentPassword] = useState("");
  const [currentGuestIndex, setCurrentGuestIndex] = useState(null);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [emailSendingStatus, setEmailSendingStatus] = useState({});

  const [stats, setStats] = useState({
    total: 0,
    onesmus: 0,
    colleta: 0,
    approved: 0,
    pending: 0,
    declined: 0,
    titles: {}
  });

  // Edit states for Late Guests
  const [editMode, setEditMode] = useState(false);
  const [editIndex, setEditIndex] = useState(null);
  const [editedGuest, setEditedGuest] = useState({
    title: "",
    name: "",
    surname: "",
    email: "",
    phone: "",
    behalfOf: "",
    approvalStatus: "pending",
    password: ""
  });

  // Fallback email function
  const sendEmailFallback = async (guest) => {
    try {
      const response = await fetch('https://wedding-email-system.vercel.app/api/send', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.REACT_APP_FALLBACK_API_TOKEN}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          to_name: `${guest.title || ""} ${guest.name} ${guest.surname}`.trim(),
          guest_email: guest.email,
          guest_password: guest.password
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success) {
        console.log('Fallback email sent successfully:', result.id);
        return true;
      } else {
        throw new Error('Fallback API returned unsuccessful response');
      }
    } catch (error) {
      console.error('Fallback email sending failed:', error);
      return false;
    }
  };

  // Enhanced email sending function with fallback
  const sendApprovalEmail = async (guest) => {
    setEmailSendingStatus(prev => ({
      ...prev,
      [guest.email]: "sending"
    }));

    // First try EmailJS
    try {
      const response = await emailjs.send(
        process.env.REACT_APP_EMAILJS_SERVICE_ID,
        process.env.REACT_APP_EMAILJS_TEMPLATE_ID,
        {
          to_name: `${guest.title || ""} ${guest.name} ${guest.surname}`.trim(),
          guest_email: guest.email,
          guest_password: guest.password,
          reply_to: "<EMAIL>"
        }
      );
      
      // Check if the response status is 200 (OK)
      if (response.status === 200) {
        console.log('EmailJS email sent successfully');
        setEmailSendingStatus(prev => ({
          ...prev,
          [guest.email]: "sent"
        }));
        return true;
      } else {
        throw new Error(`EmailJS failed with status: ${response.status}`);
      }
    } catch (emailjsError) {
      console.warn('EmailJS failed, trying fallback API:', emailjsError);
      
      // Try fallback API
      const fallbackSuccess = await sendEmailFallback(guest);
      
      if (fallbackSuccess) {
        setEmailSendingStatus(prev => ({
          ...prev,
          [guest.email]: "sent"
        }));
        return true;
      } else {
        console.error('Both EmailJS and fallback API failed');
        setEmailSendingStatus(prev => ({
          ...prev,
          [guest.email]: "error"
        }));
        return false;
      }
    }
  };

  // Update stats
  const updateStats = useCallback((data) => {
    const titleCounts = {};
    data.forEach(guest => {
      const title = guest.title || "Unspecified";
      titleCounts[title] = (titleCounts[title] || 0) + 1;
    });

    setStats({
      total: data.length,
      onesmus: data.filter((g) => g.behalfOf === "Onesmus").length,
      colleta: data.filter((g) => g.behalfOf === "Colleta").length,
      approved: data.filter((g) => g.approvalStatus === "approved").length,
      pending: data.filter((g) => g.approvalStatus === "pending").length,
      declined: data.filter((g) => g.approvalStatus === "declined").length,
      titles: titleCounts
    });
  }, []);

  // Generate password function
  const generatePassword = (length = 8) => {
    const charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
    let password = "";
    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * charset.length);
      password += charset[randomIndex];
    }
    return password;
  };

  // Initialize EmailJS
  useEffect(() => {
    emailjs.init(process.env.REACT_APP_EMAILJS_USER_ID);
  }, []);

  // Close password modal and continue
  const closePasswordModal = async () => {
    setShowPasswordModal(false);
    
    if (currentGuestIndex !== null) {
      const updatedGuests = [...lateGuests];
      updatedGuests[currentGuestIndex] = {
        ...updatedGuests[currentGuestIndex],
        password: currentPassword
      };
      
      setLateGuests(updatedGuests);
      applyFilters(searchTerm, filterBy, approvalFilter);
      
      const lateListRef = doc(db, "guests", "late_list");
      await setDoc(lateListRef, { guests: updatedGuests }, { merge: true });
      
      setCurrentGuestIndex(null);
    }
  };

  // Copy password to clipboard
  const copyPasswordToClipboard = () => {
    navigator.clipboard.writeText(currentPassword)
      .then(() => {
        alert("Password copied to clipboard!");
      })
      .catch(err => {
        console.error('Could not copy text: ', err);
      });
  };

  // Generate new password
  const regeneratePassword = () => {
    const newPassword = generatePassword();
    setCurrentPassword(newPassword);
  };

  // Fetch late guests
  const fetchLateGuests = useCallback(async () => {
    try {
      const lateListRef = doc(db, "guests", "late_list");
      const lateListDoc = await getDoc(lateListRef);

      if (lateListDoc.exists()) {
        const data = lateListDoc.data();
        let guestArray = data.guests || [];
        
        guestArray = guestArray.map(guest => ({
          ...guest,
          approvalStatus: guest.approvalStatus || "pending",
          password: guest.password || ""
        }));

        setLateGuests(guestArray);
        setFilteredGuests(guestArray);
        updateStats(guestArray);
      }
    } catch (error) {
      console.error("Error fetching late guests:", error);
    }
  }, [updateStats]);

  // Handle search
  const handleSearch = (event) => {
    const term = event.target.value.toLowerCase();
    setSearchTerm(term);
    applyFilters(term, filterBy, approvalFilter);
  };

  // Apply filters
  const applyFilters = useCallback((term, filter, approvalFilter) => {
    let filtered = lateGuests.filter((guest) => {
      const matchesSearch =
        guest.name.toLowerCase().includes(term) ||
        guest.surname.toLowerCase().includes(term) ||
        guest.email.toLowerCase().includes(term) ||
        guest.phone.includes(term);

      const matchesBehalfOf = filter === "all" || guest.behalfOf === filter;
      const matchesApproval = approvalFilter === "all" || guest.approvalStatus === approvalFilter;

      return matchesSearch && matchesBehalfOf && matchesApproval;
    });

    setFilteredGuests(filtered);
  }, [lateGuests]);

  const handleFilterChange = (event) => {
    const value = event.target.value;
    setFilterBy(value);
    applyFilters(searchTerm, value, approvalFilter);
  };

  const handleApprovalFilterChange = (event) => {
    const value = event.target.value;
    setApprovalFilter(value);
    applyFilters(searchTerm, filterBy, value);
  };

  const handleApprovalChange = async (index, status) => {
    try {
      // Find the actual index in the original lateGuests array
      const originalIndex = lateGuests.findIndex(guest => 
        guest.name === filteredGuests[index].name && 
        guest.email === filteredGuests[index].email
      );
      
      if (originalIndex === -1) {
        console.error("Guest not found in original array");
        return;
      }

      // Generate password only if changing to "approved" and no password exists
      let password = "";
      if (status === "approved" && !lateGuests[originalIndex].password) {
        password = generatePassword();
        setCurrentPassword(password);
        setCurrentGuestIndex(originalIndex);
        setShowPasswordModal(true);
      }

      // Update guest in local state
      const updatedGuests = [...lateGuests];
      updatedGuests[originalIndex] = {
        ...updatedGuests[originalIndex],
        approvalStatus: status,
        password: status === "approved" ? (password || updatedGuests[originalIndex].password || generatePassword()) : "",
      };

      // Update Firestore first to ensure database consistency
      const lateListRef = doc(db, "guests", "late_list");
      await setDoc(lateListRef, { guests: updatedGuests }, { merge: true });
      console.log(`Late guest approval status updated to ${status} in Firestore`);

      // Then update local state
      setLateGuests(updatedGuests);
      
      // Apply current filters
      applyFilters(searchTerm, filterBy, approvalFilter);
      
      // Update stats
      updateStats(updatedGuests);

      // If approving, send email with fallback support
      if (status === "approved") {
        const guest = updatedGuests[originalIndex];
        const emailSuccess = await sendApprovalEmail(guest);
        
        if (emailSuccess) {
          // Update the guest's emailSent status
          const emailSentGuests = [...updatedGuests];
          const guestIndex = emailSentGuests.findIndex(g => g.email === guest.email);
          if (guestIndex !== -1) {
            emailSentGuests[guestIndex] = {
              ...emailSentGuests[guestIndex],
              emailSent: true
            };
            
            // Update Firestore with email sent status
            await setDoc(lateListRef, { guests: emailSentGuests }, { merge: true });
            
            // Update local state
            setLateGuests(emailSentGuests);
          }
        }
      }

    } catch (error) {
      console.error(`Error ${status} late guest:`, error);
      alert(`Failed to update guest status to ${status}. Please try again.`);
    }
  };

  const exportToCSV = () => {
    const headers = ["Title", "Name", "Surname", "Email", "Phone", "Behalf Of", "Approval Status", "Password"];
    const csvData = filteredGuests.map((guest) =>
      [guest.title || "", guest.name, guest.surname, guest.email, guest.phone, guest.behalfOf, guest.approvalStatus, guest.password].join(",")
    );

    const csv = [headers.join(","), ...csvData].join("\n");
    const blob = new Blob([csv], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "late_wedding_guests.csv";
    a.click();
    window.URL.revokeObjectURL(url);
  };

  // Handle edit initiation
  const handleEditClick = (index) => {
    setEditMode(true);
    setEditIndex(index);
    // Find the actual index in the original lateGuests array
    const originalIndex = lateGuests.findIndex(guest => 
      guest.name === filteredGuests[index].name && 
      guest.email === filteredGuests[index].email
    );
    setEditIndex(originalIndex);
    setEditedGuest({...lateGuests[originalIndex]});
  };

  // Save edited guest
  const saveEditedGuest = async () => {
    try {
      // Update guest in local state
      const updatedGuests = [...lateGuests];
      updatedGuests[editIndex] = editedGuest;
      
      setLateGuests(updatedGuests);
      
      // Apply current filters
      applyFilters(searchTerm, filterBy, approvalFilter);
      
      // Update stats
      updateStats(updatedGuests);
      
      // Update Firestore
      const lateListRef = doc(db, "guests", "late_list");
      await setDoc(lateListRef, { guests: updatedGuests }, { merge: true });
      
      // Reset edit mode
      setEditMode(false);
      setEditIndex(null);
      setEditedGuest({
        title: "",
        name: "",
        surname: "",
        email: "",
        phone: "",
        behalfOf: "",
        approvalStatus: "pending",
        password: ""
      });
      
      console.log("Late guest updated successfully");
    } catch (error) {
      console.error("Error updating late guest:", error);
    }
  };

  // Cancel editing
  const cancelEdit = () => {
    setEditMode(false);
    setEditIndex(null);
    setEditedGuest({
      title: "",
      name: "",
      surname: "",
      email: "",
      phone: "",
      behalfOf: "",
      approvalStatus: "pending",
      password: ""
    });
  };

  // Handle input changes in edit form
  const handleEditInputChange = (e) => {
    const { name, value } = e.target;
    setEditedGuest(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Delete guest
  const deleteGuest = async (index) => {
    if (!window.confirm("Are you sure you want to delete this guest?")) return;

    try {
      // Find the actual index in the original lateGuests array
      const originalIndex = lateGuests.findIndex(guest => 
        guest.name === filteredGuests[index].name && 
        guest.email === filteredGuests[index].email
      );
      
      // Remove the guest from local state
      const updatedGuests = [...lateGuests];
      updatedGuests.splice(originalIndex, 1);
      setLateGuests(updatedGuests);
      setFilteredGuests(updatedGuests);
      updateStats(updatedGuests);

      // Update Firestore
      const lateListRef = doc(db, "guests", "late_list");
      await setDoc(lateListRef, { guests: updatedGuests }, { merge: true });

      console.log("Late guest deleted successfully");
    } catch (error) {
      console.error("Error deleting late guest:", error);
    }
  };

  useEffect(() => {
    fetchLateGuests();
  }, [fetchLateGuests]);

  const getChartColors = (type) => {
    if (type === 'behalfOf') {
      return {
        backgroundColors: ["#6D8B74", "#D9CAB3"],
        hoverColors: ["#5A7561", "#B8A992"]
      };
    } else if (type === 'approval') {
      return {
        backgroundColors: ["#4CAF50", "#FFC107", "#F44336"],
        hoverColors: ["#43A047", "#FFB300", "#E53935"]
      };
    } else {
      const baseColors = [
        '#6D8B74', '#D9CAB3', '#5F7161', '#EFEAD8', 
        '#4B644A', '#C2B092', '#3C4F3A', '#A29171'
      ];
      const backgroundColors = [];
      const hoverColors = [];
      const titles = Object.keys(stats.titles);
      titles.forEach((_, index) => {
        const colorIndex = index % baseColors.length;
        backgroundColors.push(baseColors[colorIndex]);
        hoverColors.push(baseColors[colorIndex] + '90');
      });
      return { backgroundColors, hoverColors };
    }
  };

  const getChartData = () => {
    if (statView === "behalfOf") {
      return {
        labels: ["Onesmus' Guests", "Colleta's Guests"],
        datasets: [{
          data: [stats.onesmus, stats.colleta],
          backgroundColor: getChartColors('behalfOf').backgroundColors,
          hoverBackgroundColor: getChartColors('behalfOf').hoverColors,
        }]
      };
    }
    if (statView === "approval") {
      return {
        labels: ["Approved", "Pending", "Declined"],
        datasets: [{
          data: [stats.approved, stats.pending, stats.declined],
          backgroundColor: getChartColors('approval').backgroundColors,
          hoverBackgroundColor: getChartColors('approval').hoverColors,
        }]
      };
    }
    if (statView === "titles") {
      return {
        labels: Object.keys(stats.titles),
        datasets: [{
          data: Object.values(stats.titles),
          backgroundColor: getChartColors('titles').backgroundColors,
          hoverBackgroundColor: getChartColors('titles').hoverColors,
        }]
      };
    }
  };

  // Password modal component
  const PasswordModal = () => (
    <div className="password-modal-overlay">
      <div className="password-modal">
        <h2>Guest Password Generated</h2>
        <p>A password has been generated for {lateGuests[currentGuestIndex]?.name} {lateGuests[currentGuestIndex]?.surname}:</p>
        
        <div className="password-display">
          <input type="text" value={currentPassword} readOnly />
          <button onClick={copyPasswordToClipboard} className="copy-button">Copy</button>
        </div>
        
        <p>You can provide this password to the guest for their access.</p>
        
        <div className="password-modal-actions">
          <button onClick={regeneratePassword} className="regenerate-button">Generate New Password</button>
          <button onClick={closePasswordModal} className="confirm-button">Confirm</button>
        </div>
      </div>
    </div>
  );

  // Edit form component
  const EditForm = () => (
    <div className="edit-form-overlay">
      <div className="edit-form">
        <h2>Edit Late Guest</h2>
        <div className="form-group">
          <label>Title:</label>
          <input
            type="text"
            name="title"
            value={editedGuest.title}
            onChange={handleEditInputChange}
          />
        </div>
        <div className="form-group">
          <label>Name:</label>
          <input
            type="text"
            name="name"
            value={editedGuest.name}
            onChange={handleEditInputChange}
          />
        </div>
        <div className="form-group">
          <label>Surname:</label>
          <input
            type="text"
            name="surname"
            value={editedGuest.surname}
            onChange={handleEditInputChange}
          />
        </div>
        <div className="form-group">
          <label>Email:</label>
          <input
            type="email"
            name="email"
            value={editedGuest.email}
            onChange={handleEditInputChange}
          />
        </div>
        <div className="form-group">
          <label>Phone:</label>
          <input
            type="text"
            name="phone"
            value={editedGuest.phone}
            onChange={handleEditInputChange}
          />
        </div>
        <div className="form-group">
          <label>Behalf Of:</label>
          <select
            name="behalfOf"
            value={editedGuest.behalfOf}
            onChange={handleEditInputChange}
          >
            <option value="Onesmus">Onesmus</option>
            <option value="Colleta">Colleta</option>
          </select>
        </div>
        <div className="form-group">
          <label>Approval Status:</label>
          <select
            name="approvalStatus"
            value={editedGuest.approvalStatus}
            onChange={handleEditInputChange}
          >
            <option value="pending">Pending</option>
            <option value="approved">Approved</option>
            <option value="declined">Declined</option>
          </select>
        </div>
        {editedGuest.approvalStatus === "approved" && (
          <div className="form-group">
            <label>Password:</label>
            <div className="password-input-group">
              <input
                type="text"
                name="password"
                value={editedGuest.password}
                onChange={handleEditInputChange}
              />
              <button
                type="button"
                onClick={() => {
                  const newPassword = generatePassword();
                  setEditedGuest(prev => ({...prev, password: newPassword}));
                }}
                className="generate-password-button"
              >
                Generate New
              </button>
            </div>
          </div>
        )}
        <div className="form-buttons">
          <button onClick={saveEditedGuest}>Save</button>
          <button onClick={cancelEdit} className="cancel-button">Cancel</button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="container">
      <h1>Late Guest Dashboard</h1>
      <p className="subtitle">Managing extra guests from the late_list collection</p>

      {/* Stats */}
      <div className="stats-container">
        <div className="stats-card"><p>Total Late Guests</p><span>{stats.total}</span></div>
        <div className="stats-card"><p>Onesmus's Late Guests</p><span>{stats.onesmus}</span></div>
        <div className="stats-card"><p>Colleta's Late Guests</p><span>{stats.colleta}</span></div>
        <div className="stats-card"><p>Approved</p><span>{stats.approved}</span></div>
        <div className="stats-card"><p>Pending</p><span>{stats.pending}</span></div>
        <div className="stats-card"><p>Declined</p><span>{stats.declined}</span></div>
      </div>

      {/* Chart View Toggle */}
      <div className="view-toggle">
        <button 
          onClick={() => setStatView("behalfOf")} 
          className={`toggle-button ${statView === "behalfOf" ? "active" : ""}`}
        >
          View by Behalf Of
        </button>
        <button 
          onClick={() => setStatView("titles")} 
          className={`toggle-button ${statView === "titles" ? "active" : ""}`}
        >
          View by Titles (Mr/Mrs)
        </button>
        <button 
          onClick={() => setStatView("approval")} 
          className={`toggle-button ${statView === "approval" ? "active" : ""}`}
        >
          View by Approval Status
        </button>
      </div>

      {/* Single Chart Area */}
      <div className="chart-container">
        <Pie data={getChartData()} />
      </div>

      {/* Filters */}
      <div className="filters">
        <input type="text" placeholder="Search late guests..." value={searchTerm} onChange={handleSearch} />
        <select value={filterBy} onChange={handleFilterChange}>
          <option value="all">All Guests</option>
          <option value="Onesmus">Onesmus's Guests</option>
          <option value="Colleta">Colleta's Guests</option>
        </select>
        <select value={approvalFilter} onChange={handleApprovalFilterChange}>
          <option value="all">All Statuses</option>
          <option value="pending">Pending</option>
          <option value="approved">Approved</option>
          <option value="declined">Declined</option>
        </select>
        <button onClick={exportToCSV}>Export CSV</button>
      </div>

      {/* Table */}
      <div className="table-container">
        <table>
          <thead>
            <tr>
              <th>Title</th>
              <th>Name</th>
              <th>Surname</th>
              <th>Email</th>
              <th>Phone</th>
              <th>Behalf Of</th>
              <th>Status</th>
              <th>Password</th>
              <th>Email Sent</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredGuests.map((guest, index) => (
              <tr key={index} className={`status-${guest.approvalStatus}`}>
                <td>{guest.title || "—"}</td>
                <td>{guest.name}</td>
                <td>{guest.surname}</td>
                <td>{guest.email}</td>
                <td>{guest.phone}</td>
                <td>{guest.behalfOf}</td>
                <td className={`status-cell ${guest.approvalStatus}`}>
                  {guest.approvalStatus.charAt(0).toUpperCase() + guest.approvalStatus.slice(1)}
                </td>
                <td>{guest.password ? "••••••••" : "—"}</td>
                <td className="email-sent-cell">
                  {guest.approvalStatus === "approved" && (
                    emailSendingStatus[guest.email] === "sending" ? (
                      <span className="sending-email">Sending...</span>
                    ) : emailSendingStatus[guest.email] === "sent" || guest.emailSent ? (
                      <span className="email-status sent">✓ Sent</span>
                    ) : emailSendingStatus[guest.email] === "error" ? (
                      <span className="email-status error">✗ Failed</span>
                    ) : (
                      <span className="email-status not-sent">Not Sent</span>
                    )
                  )}
                </td>
                <td className="action-buttons">
                  {guest.approvalStatus !== "approved" && (
                    <button 
                      onClick={() => handleApprovalChange(index, "approved")} 
                      className="approve-button"
                      title="Approve"
                    >
                      ✓
                    </button>
                  )}
                  {guest.approvalStatus !== "declined" && (
                    <button 
                      onClick={() => handleApprovalChange(index, "declined")} 
                      className="decline-button"
                      title="Decline"
                    >
                      ✗
                    </button>
                  )}
                  <button 
                    onClick={() => handleEditClick(index)} 
                    className="edit-button"
                    title="Edit"
                  >
                    ✎
                  </button>
                  <button 
                    onClick={() => deleteGuest(index)} 
                    className="delete-button"
                    title="Delete"
                  >
                    🗑️
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Edit Form Modal */}
      {editMode && <EditForm />}
      
      {/* Password Modal */}
      {showPasswordModal && <PasswordModal />}
    </div>
  );
};

export default LateGuestsDashboard;